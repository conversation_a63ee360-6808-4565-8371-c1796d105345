{"name": "@vue/eslint-config-standard", "version": "6.1.0", "description": "eslint-config-standard for Vue CLI", "main": "index.js", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/eslint-config-standard.git"}, "keywords": ["vue", "cli"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/eslint-config-standard/issues"}, "homepage": "https://github.com/vuejs/eslint-config-standard#readme", "dependencies": {"eslint-config-standard": "^16.0.3", "eslint-import-resolver-node": "^0.3.4", "eslint-import-resolver-webpack": "^0.13.1"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": "^7.12.1", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1 || ^5.0.0", "eslint-plugin-vue": "^7.0.0"}, "peerDependenciesMeta": {"@vue/cli-service": {"optional": true}}}