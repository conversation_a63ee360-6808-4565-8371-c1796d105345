{"name": "sass-loader", "version": "12.6.0", "description": "Sass loader for webpack", "license": "MIT", "repository": "webpack-contrib/sass-loader", "author": "<PERSON><PERSON>", "homepage": "https://github.com/webpack-contrib/sass-loader", "bugs": "https://github.com/webpack-contrib/sass-loader/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 12.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit --production", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:manual": "npm run build && webpack-dev-server test/manual/src/index.js --open --config test/manual/webpack.config.js", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky install && npm run build", "release": "standard-version"}, "files": ["dist"], "peerDependencies": {"fibers": ">= 3.1.0", "node-sass": "^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0", "sass": "^1.3.0", "sass-embedded": "*", "webpack": "^5.0.0"}, "peerDependenciesMeta": {"node-sass": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}, "fibers": {"optional": true}}, "dependencies": {"klona": "^2.0.4", "neo-async": "^2.6.2"}, "devDependencies": {"@babel/cli": "^7.17.0", "@babel/core": "^7.17.0", "@babel/preset-env": "^7.16.11", "@commitlint/cli": "^16.2.1", "@commitlint/config-conventional": "^16.2.1", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^27.5.0", "bootstrap-sass": "^3.4.1", "bootstrap-v4": "npm:bootstrap@^4.5.3", "bootstrap-v5": "npm:bootstrap@^5.0.1", "cross-env": "^7.0.3", "css-loader": "^6.6.0", "del": "^6.0.0", "del-cli": "^4.0.1", "enhanced-resolve": "^5.8.2", "eslint": "^8.8.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.25.4", "fibers": "^5.0.1", "file-loader": "^6.2.0", "foundation-sites": "^6.6.3", "husky": "^7.0.1", "jest": "^27.5.0", "jest-environment-node-single-context": "^27.2.0", "lint-staged": "^12.3.3", "material-components-web": "^8.0.0", "memfs": "^3.4.1", "node-sass": "^7.0.1", "node-sass-glob-importer": "^5.3.2", "npm-run-all": "^4.1.5", "prettier": "^2.3.2", "sass": "^1.49.7", "sass-embedded": "^1.49.7", "semver": "^7.3.5", "standard-version": "^9.3.1", "style-loader": "^3.2.1", "webpack": "^5.68.0"}, "keywords": ["sass", "libsass", "webpack", "loader"]}